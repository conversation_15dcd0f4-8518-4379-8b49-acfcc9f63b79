package com.reagent.order.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/14 17:36
 * @description 订单事件枚举，管理所有的推送到第三方平台的枚举，并与日志表order_event_status相关，推送结果会记录在表中，对应字段event_type
 * 外部需要推送事件的回调时，实现接口OrderEventPushResultService，取对应的事件枚举进行处理即可
 *
 * 推送事件定义：业务事件（如订单事件中的订单验收/订单确认等时间）+推送接收方的结合体
 * 用于定位具体是哪个推送事件成功或失败，及快速定位到需要重推的事件及对象来进行重新推送
 */
public enum OrderPushEventEnum {
    
    // 外部采购人的（即对接了管理平台的）
    
    /**
     * 发票推送
     */
    INVOICE_PUSH(1,"发票推送"),

    /**
     * 取消订单完成，推送到外部管理平台
     */
    CANCEL_ORDER_COMPLETE_TO_OUTER_BUYER(3,"取消订单完成，推送到外部管理平台"),


    /**
     * 查询管理平台状态
     */
    QUERY_OUTER_BUYER_STATUS(5,"查询管理平台状态"),

    /**
     * 订单生成
     */
    GENERATE_ORDER(6,"订单生成"),

    /**
     * 订单入库
     */
    WAREHOUSE(7,"订单入库"),

    /**
     * 取消订单完成，推送到外部管理平台
     */
    SUPPLIER_REJECT_CANCEL_ORDER_TO_OUTER_BUYER(8,"供应商拒绝取消订单，推送到外部管理平台"),

    /**
     * 通知发货
     */
    NOTICE_DELIVERY(9, "供应商订单发货"),

    /**
     * 通知管理平台收货，推送到外部管理平台
     */
    NOTICE_RECEIVE_TO_OUTER_BUYER(10, "已收货，推送到外部管理平台"),

    /**
     * 通知退货结果，推送到外部管理平台
     */
    NOTICE_RETURN_GOODS_RESULT_TO_OUTER_BUYER(11, "通知退货结果，推送到外部管理平台"),


    /**
     * 供应商确认订单，推送到外部管理平台
     */
    NOTICE_SUPP_CONFIRM(14, "供应商确认订单"),

    /**
     * 推送预订单
     */
    BID_PUSH_PRE_ORDER(16, "竞价推送预订单"),

    /**
     * 供应商申请取消订单
     */
    SUPP_APPLY_CANCEL_ORDER(17, "供应商申请取消订单"),

    /**
     * 用户确认收货，订单状态变更为  待验收审批
     * 请使用NOTICE_RECEIVE_TO_OUTER_BUYER
     */
    @Deprecated
    BUYER_ACCEPT(21, "用户确认收货"),


    /**
     * 验收审批通过，状态变为待结算
     */
    ACCEPT_APPROVAL(22, "验收审批通过"),


    /**
     * 用户发起结算
     */
    NOTICE_CREATE_SETTLEMENT(23, "发起结算"),

    NOTICE_ORDER_CREATE_TO_OUTER_BUYER(24, "订单生成"),

    /**
     * 订单非正常途径关闭，一般是OMS异常关单
     */
    ORDER_ABNORMAL_CLOSE(25, "订单非正常流程关闭"),

    /**
     * 结算完成，订单完成
     */
    STATEMENT_FINISH(26, "结算完成，订单完成"),

    /**
     * 试用订单验收，订单关闭
     */
    TRAIL_ORDER_ACCEPT(27, "试用订单验收，订单关闭"),

    /**
     * 汇总开票
     */
    SUMMARY_INVOICE(28, "汇总开票"),

    /**
     * 移除汇总
     */
    REMOVE_SUMMARY(29, "移除汇总"),

    /**
     * 采购人发起退货，推送到外部供应商
     */
    BUYER_APPLY_RETURN_GOODS_TO_SUPPLIER(30, "采购人发起退货"),

    /**
     * 采购人 提交报销申请 江西中医附院
     */
    BUYER_APPLY_REIMBURSEMENT(31, "采购人提交报销申请"),


    /**
     * 竞价审批通过，待生成订单 浙肿
     */
    BID_APPROVAL_PASSED_PENDING_GENERATE_ORDER(32, "竞价审批通过，待生成订单"),

    /**
     * 订单拆分母单被关闭
     */
    SPLIT_ORDER_PARENT_CLOSED(33, "订单拆分母单被关闭"),

    /**
     * 订单出库
     */
    EX_WAREHOUSE(34, "订单出库"),

    /**
     * 用于某些单位关注订单拆分前后的母单和子单，需要所有子单关闭后，进行母单关闭推送。如广州实验室
     */
    ALL_CHILD_ORDER_CLOSE(35, "所有子单关闭"),

    /**
     * 通知管理平台，供应商取消发货
     */
    NOTICE_SUPPLIER_CANCEL_DELIVER_TO_OUTER_BUYER(36, "通知管理平台，供应商取消发货"),

    STATEMENT_EDIT_INVOICE(37, "结算环节修改发票"),

    STATEMENT_FINANCE_REJECT(38, "结算环节财务驳回"),

    CANCEL_STATEMENT(39, "撤销结算到待结算"),

    STATEMENT_SUBMIT_FINANCE_SUCCESS(40, "结算环节提交财务成功"),

    // 外部供应商的

    /**
     * 待确认状态，推送订单到供应商平台，关联状态：待确认
     */
    PUSH_TO_SUPPLIER(2,"推送订单到供应商平台"),

    /**
     * 待发货状态，采购人取消订单推送到供应商
     */
    BUYER_CANCEL_TO_SUPPLIER_WHEN_WAITING_FOR_DELIVERY(4,"采购人待发货状态取消订单"),
    ;

    private final Integer value;
    
    private final String name;

    OrderPushEventEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static OrderPushEventEnum getByValue(Integer value) {
        for (OrderPushEventEnum orderEventEnum : OrderPushEventEnum.values()) {
            if (Objects.equals(orderEventEnum.value, value)) {
                return orderEventEnum;
            }
        }
        return null;
    }
}
