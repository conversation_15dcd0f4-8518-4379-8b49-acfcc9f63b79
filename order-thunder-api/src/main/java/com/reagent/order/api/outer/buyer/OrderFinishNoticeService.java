package com.reagent.order.api.outer.buyer;

import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.ruijing.base.swagger.api.rpc.annotation.RpcApi;
import com.ruijing.base.swagger.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;

/**
 * <AUTHOR>
 * @date 2022/12/2 10:50
 * @description
 */
@RpcApi(
        value = "完成订单相关状态通知服务",
        description = "完成订单相关状态通知服务"
)
public interface OrderFinishNoticeService {

    /**
     * 订单完成
     * @param outerBuyerCommonProcessDTO 订单信息
     * @return 是否成功
     */
    @RpcMethod("通知订单完成")
    default RemoteResponse<Boolean> finishOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {return RemoteResponse.<Boolean>custom().setSuccess().setData(true);}
}
