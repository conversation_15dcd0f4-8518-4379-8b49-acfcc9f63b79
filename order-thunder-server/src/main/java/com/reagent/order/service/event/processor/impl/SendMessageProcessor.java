package com.reagent.order.service.event.processor.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.config.docking.event.enums.OrderEvent;
import com.reagent.order.config.docking.event.enums.OrderStatusRelatedEventEnum;
import com.reagent.order.dto.OrderPushDTO;
import com.reagent.order.service.event.processor.OrderEventProcessor;
import com.reagent.order.service.message.messageProcessor.AbstractOrderMessageProcessor;
import com.reagent.order.service.message.messageProcessor.factory.MessageProcessorFactory;
import com.reagent.order.service.message.messageSender.constant.MessageSenderConstant;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: ch<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2025-05-21 17:02
 * @description:
 **/
@Service
public class SendMessageProcessor implements OrderEventProcessor {

    /**
     * 发送方式对应发送渠道
     */
    public static Map<Integer, String> SENDING_WAY_TO_SENDER_MAP = New.map();

    /**
     * 站内信 渠道
     * 由于com.ruijing.store.cms.api.enums.SendingWayEnum 暂时没有 手动先加上
     */
    public static Integer LETTER_TYPE = 99;

    static {
        SENDING_WAY_TO_SENDER_MAP.put(SendingWayEnum.EMAIL.getValue(), MessageSenderConstant.EMAIL_SENDER);
        SENDING_WAY_TO_SENDER_MAP.put(LETTER_TYPE, MessageSenderConstant.LETTER_SENDER);
    }

    @Resource
    private MessageProcessorFactory messageProcessorFactory;


    /**
     * 1.根据事件获取到对应的消息处理器 MessageProcessor
     * 2.由消息处理器决定当前需要发送什么类型消息（邮件，微信...），发送的场景id,以及接收方是谁
     *
     *
     * @param orderPushDTO 订单变更数据
     * @param orderEventList 订单事件
     */
    @Override
    public void notice(OrderPushDTO orderPushDTO, List<OrderEvent> orderEventList) {
        for (OrderEvent orderEvent : orderEventList) {
            if(orderEvent instanceof OrderStatusRelatedEventEnum){
                if(OrderStatusRelatedEventEnum.ORDER_FINISH.equals(orderEvent)){
                    AbstractOrderMessageProcessor messageProcessor = messageProcessorFactory.getMessageProcessor(OrderStatusRelatedEventEnum.ORDER_FINISH.name());
                    if(Objects.isNull(messageProcessor)){
                        continue;
                    }
                    if(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue() != orderPushDTO.getOrgId()){
                        messageProcessor.sendMessage(orderPushDTO);
                    }
                } else if (OrderStatusRelatedEventEnum.SUPPLIER_CANCEL_DELIVER.equals(orderEvent)) {
                    AbstractOrderMessageProcessor messageProcessor = messageProcessorFactory.getMessageProcessor(OrderStatusRelatedEventEnum.SUPPLIER_CANCEL_DELIVER.name());
                    if(Objects.isNull(messageProcessor)){
                        continue;
                    }
                    messageProcessor.sendMessage(orderPushDTO);
                }
            }
        }
    }
}
