package com.reagent.order.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.api.OrderForPlatformRPCService;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.config.docking.DockingConfigManager;
import com.reagent.order.constant.DockingOuterBuyerConstant;
import com.reagent.order.dto.HandleOuterSupplierPushDTO;
import com.reagent.order.dto.OrderTpiDockingLogDTO;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.OrderDataQryRequestDTO;
import com.reagent.order.dto.request.ThirdPartBaseOrderRequestDTO;
import com.reagent.order.dto.request.ThirdPartQueryRequestDTO;
import com.reagent.order.dto.request.ThirdPartUpdatedOrderDTO;
import com.reagent.order.dto.request.supplier.OuterSupplierOrderDTO;
import com.reagent.order.dto.request.supplier.OuterSupplierRequestDTO;
import com.reagent.order.dto.response.ThirdPartQueryOrderDetailResponseDTO;
import com.reagent.order.dto.response.ThirdPartQueryOrderInvoiceResponseDTO;
import com.reagent.order.dto.response.ThirdPartQueryOrderResponseDTO;
import com.reagent.order.dto.response.ThirdPartUpdateResponseDTO;
import com.reagent.order.enums.ActiveOneEnum;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterSupplierEnum;
import com.reagent.order.enums.SupplierEventTypeEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.order.enums.log.DockingCallDirection;
import com.reagent.order.log.service.OrderTpiDockingLogService;
import com.reagent.order.push.PassivePushHandler;
import com.reagent.order.rpc.*;
import com.reagent.order.rpc.application.ApplicationBusinessRpcClient;
import com.reagent.order.rpc.oms.OmsSysClient;
import com.reagent.order.service.factory.OrderAdapterServiceFactory;
import com.reagent.order.service.factory.SupplierAdapterServiceFactory;
import com.reagent.order.service.supplier.PushFromSupplierAdapterService;
import com.reagent.order.utils.OrderCommonUtils;
import com.reagent.order.utils.OrderTpiDockingLogUtils;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceRefResultDTO;
import com.reagent.research.statement.api.statement.dto.StatementWayConfigDTO;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.shop.crm.api.pojo.dto.ContactManDTO;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.pojo.dto.bank.BankAccountDTO;
import com.ruijing.shop.trade.api.dto.OrderSupplierRemarkDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplyPreOrderMasterDTO;
import com.ruijing.store.apply.dto.ApplyRefFundCardDTO;
import com.ruijing.store.apply.dto.PreOrderQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.batchcode.api.batches.dto.OrderProductBatchesDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDetailDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.extend.UserExtendInfoDTO;
import com.ruijing.store.user.api.enums.user.UserExtendInfoTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@MSharpService
@CatAnnotation
public class OrderForPlatformRPCServiceImpl implements OrderForPlatformRPCService {

    /**
     * 需要输出批次的单位
     */
    private final List<String> needOutPutBatchesOrgCodeList = New.list(OrgEnum.JIANG_XI_ZHONG_LIU.getCode(), OrgEnum.SHEN_ZHEN_SHI_DI_SAN_REN_MIN_YI_YUAN.getCode(),OrgEnum.SHEN_ZHEN_SHI_REN_MIN_YI_YUAN.getCode());
    
    private static final String CLASS_NAME = "OrderForPlatformRPCServiceImpl";

    private final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "OrderForThirdPartRPCServiceImpl";

    @Resource
    private OrderMasterCommonRPCClient orderMasterCommonRPCClient;

    @Resource
    private OrderDetailRPCClient orderDetailRPCClient;

    @Resource
    private OrderRelatedRPCClient orderRelatedRPCClient;

    @Resource
    private ResearchFundCardRPCClient researchFundCardRPCClient;

    @Resource
    private InvoiceRPCClient invoiceRPCClient;

    @Resource
    private SuppInfoRPCClient suppInfoRPCClient;

    @Resource
    private ApplicationBaseRPCClient applicationBaseRPCClient;

    @Resource
    private UserRPCClient userRPCClient;

    @Resource
    private SuppBankRPCClient suppBankRPCClient;

    @Resource
    private OrderAdapterServiceFactory orderAdapterServiceFactory;

    @Resource
    private SupplierAdapterServiceFactory supplierAdapterServiceFactory;

    @Resource
    protected OrderTpiDockingLogService orderTpiDockingLogService;

    @Resource
    private DockingConfigManager dockingConfigManager;

    @Resource
    private PassivePushHandler passivePushHandler;

    @Resource
    private ApplicationBusinessRpcClient applicationBusinessRpcClient;

    @Resource
    private OrderBatchRpcServiceClient orderBatchRpcServiceClient;

    @Resource
    private DepartmentRPCClient departmentRPCClient;


    @Resource
    private OrderAddressRPCClient orderAddressRpcClient;


    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private GoodsReturnRPCClient goodsReturnRPCClient;

    @Resource
    private OrderSearchRPCClient orderSearchRPCClient;

    @Resource
    private OmsSysClient omsSysClient;

    @Resource
    private ResearchStatementRPCClient researchStatementRPCClient;

    @Override
    @ServiceLog(description = "管理平台推送到thunder操作", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<ThirdPartUpdateResponseDTO<String>> thirdPartyUpdateOrder(ThirdPartBaseOrderRequestDTO request) {
        String orgCode = request.getOrgCode();
        OrderEventTypeEnum eventType = request.getOrderEventType();
        ActiveOneEnum activeOneType = request.getActiveOneType();
        List<ThirdPartUpdatedOrderDTO> updatedOrderList = request.getUpdatedOrderList();
        Preconditions.notNull(orgCode, "机构编码不能为空");
        Preconditions.notNull(eventType, "更新订单事件不能为空");
        Preconditions.isTrue(activeOneType == ActiveOneEnum.THIRD_PART_PLATFORM, "activeOneType 必须为 ActiveOneEnum.THIRD_PART_PLATFORM");
        Preconditions.notEmpty(updatedOrderList, "更新订单入参不能为空");

        ThirdPartUpdateResponseDTO<String> result = new ThirdPartUpdateResponseDTO<>();
        result.setOrgCode(orgCode);
        result.setThirdPartUpdateOrderResponseDTOList(updatedOrderList.stream().map(ThirdPartUpdatedOrderDTO::getOrderNoList).flatMap(List::stream).collect(Collectors.toList()));

        // 如果该单位没有配置，走旧代码
        OrgDockingConfigDTO config = dockingConfigManager.getConfigDTO(orgCode);
        if(config.getEnable() || config.getOldExist()){
            return passivePushHandler.handle(request).setData(result);
        }

        if (!DockingOuterBuyerConstant.getAllDockingBuyerSet().contains(orgCode)) {
            return RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setFailure("推送失败，非对接管理平台的单位！");
        }
        orderAdapterServiceFactory.getBean(orgCode).executeForRJ(request, eventType);
        return RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "第三方对接单位查询我方订单信息接口")
    public RemoteResponse<List<ThirdPartQueryOrderResponseDTO>> thirdPartyQueryOrder(ThirdPartQueryRequestDTO request) {
        List<String> orderNoList = request.getOrderNoList();
        String orgCode = request.getOrgCode();
        Preconditions.notEmpty(orderNoList, "订单号不能为空");
        Preconditions.notNull(orgCode, "单位编码不能为空");

        //通过订单号查询订单主表信息，便于接下来通过主表信息搜索其他信息
        List<OrderMasterDTO> orderList = orderMasterCommonRPCClient.findByOrderNoList(orderNoList);
        if (CollectionUtils.isEmpty(orderList)) {
            OrgDockingConfigDTO config = dockingConfigManager.getConfigDTO(orgCode);
            boolean enableNewPushApp = config.getEnable() && OmsDockingConfigValueEnum.PUSH_PRE_ORDER.name().equals(config.getAppDockingConfigDTO().getAppPushOrderData());
            boolean enableOldPushApp = !config.getEnable() && config.getOldExist() && config.getStrategyEnumList().contains(OrderDockingStrategyEnum.APP_PUSH_PRE_ORDER);
            if (!enableNewPushApp && !enableOldPushApp) {
                // 不是推预订单的单位，查不到
                return RemoteResponse.success(New.emptyList());
            }
            return RemoteResponse.success(this.fetchPreOrderInfo(orderNoList, orgCode));
        }
        List<ThirdPartQueryOrderResponseDTO> result = null;
        try {
            // 过滤掉非本单位的
            orderList = orderList.stream().filter(orderMasterDTO -> orgCode.equals(orderMasterDTO.getFusercode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderList)){
                return RemoteResponse.success(New.emptyList());
            }
            result = this.fetchThirdPartyQueryOrderInfo(orderList, orgCode);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
        return RemoteResponse.<List<ThirdPartQueryOrderResponseDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public PageableResponse<List<ThirdPartQueryOrderResponseDTO>> searchOrderForOuterPlatform(OrderDataQryRequestDTO requestDTO) {
        BusinessErrUtil.notNull(requestDTO.getOrgCode(), "必须指定需要查询的单位");
        BusinessErrUtil.isTrue(requestDTO.getPageSize() <= 20, "最多一次查询20个单据");
        Integer orgId = omsSysClient.getOrgIdByCode(requestDTO.getOrgCode());
        Integer buyerId = null;
        BusinessErrUtil.notNull(orgId, "无效的单位");

        // 参数封装，调用搜索拿到单号和页码
        if(StringUtils.isNotEmpty(requestDTO.getBuyerJobNumber())){
            List<UserBaseInfoDTO> userBaseInfoDTOList = userRPCClient.getUserByJobNumAndOrgId(New.list(requestDTO.getBuyerJobNumber()), orgId);
            if(CollectionUtils.isNotEmpty(userBaseInfoDTOList)){
                buyerId = userBaseInfoDTOList.get(0).getId();
            }
        }

        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setOrgIdList(New.list(orgId));
        orderSearchParamDTO.setBuyerIdList(buyerId == null ? null : New.list(buyerId));
        if(StringUtils.isNotEmpty(requestDTO.getOriginalOrderNo())){
            // 这里是分词查询(PhraseQuery)，可以查得到拆分后的订单
            orderSearchParamDTO.setOrderNo(requestDTO.getOriginalOrderNo());
        }
        orderSearchParamDTO.setStatusList(requestDTO.getStatusList());
        orderSearchParamDTO.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        orderSearchParamDTO.setStartHit((requestDTO.getPageNo() - 1) * requestDTO.getPageSize());
        orderSearchParamDTO.setPageSize(requestDTO.getPageSize());
        orderSearchParamDTO.addSourceField("forderno");
        SearchPageResultDTO<OrderMasterSearchDTO> searchPageResultDTO = orderSearchRPCClient.commonSearch(orderSearchParamDTO);
        if(CollectionUtils.isEmpty(searchPageResultDTO.getRecordList())){
            return PageableResponse.<List<ThirdPartQueryOrderResponseDTO>>custom().setData(New.emptyList()).setPageNo(requestDTO.getPageNo()).setPageSize(requestDTO.getPageSize()).setSuccess();
        }
        // 借用原有查询转换订单
        List<String> orderNos = searchPageResultDTO.getRecordList().stream().map(OrderMasterSearchDTO::getForderno).collect(Collectors.toList());
        ThirdPartQueryRequestDTO thirdPartQueryRequestDTO = new ThirdPartQueryRequestDTO();
        thirdPartQueryRequestDTO.setOrgCode(requestDTO.getOrgCode());
        thirdPartQueryRequestDTO.setOrderNoList(orderNos);
        RemoteResponse<List<ThirdPartQueryOrderResponseDTO>> convertOrderResp = this.thirdPartyQueryOrder(thirdPartQueryRequestDTO);
        return PageableResponse.<List<ThirdPartQueryOrderResponseDTO>>custom().setData(convertOrderResp.getData()).setPageSize(requestDTO.getPageSize()).setPageNo(requestDTO.getPageNo()).setTotal(searchPageResultDTO.getTotalHits()).setSuccess();
    }

    @Override
    @ServiceLog(description = "第三方供应商调用我方订单接口")
    public RemoteResponse<ThirdPartUpdateResponseDTO<String>> updateOrderForOuterSupplierPlatform(OuterSupplierRequestDTO request) {
        final String methodName = "updateOrderForOuterSupplierPlatform";
        
        String supplierCode = request.getSupplierCode();
        SupplierEventTypeEnum eventType = request.getSupplierEventTypeEnum();
        Preconditions.notNull(eventType, "更新订单事件不能为空");
        List<OuterSupplierOrderDTO> outerSupplierRequestDTOList = request.getOuterSupplierOrderDTOList();
        Preconditions.notEmpty(request.getOuterSupplierOrderDTOList(), "订单数据不能为空");

        ThirdPartUpdateResponseDTO<String> result = new ThirdPartUpdateResponseDTO<>();
        result.setOrgCode(supplierCode);
        result.setThirdPartUpdateOrderResponseDTOList(outerSupplierRequestDTOList.stream().map(OuterSupplierOrderDTO::getOrderNo).collect(Collectors.toList()));
        
        // 通过供应商编号找到供应商类型，没有则为非对接的供应商，不对接则跳过
        OuterSupplierEnum supplierType = OuterSupplierEnum.getBySupplierCode(supplierCode);
        if (supplierType == null) {
            return RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setSuccess().setData(result);
        }
        // 只能一次推送一个单，所以取第1个
        HandleOuterSupplierPushDTO handleOuterSupplierPushDto = this.getHandleOuterSupplierPushDtoList(request).get(0);
        
        // 记日志
        OrderTpiDockingLogDTO orderTpiDockingLogDTO = OrderTpiDockingLogUtils.initialOrderTpiDockingLogDTO(supplierCode, handleOuterSupplierPushDto.getOrderNo(),
                request, DockingCallDirection.TPI_TO_RUIJING_INDIRECTLY, CLASS_NAME, methodName);
        
        orderTpiDockingLogService.insertOrderTpiDockingLog(orderTpiDockingLogDTO);
        // 选择工厂策略
        PushFromSupplierAdapterService pushFromSupplierAdapterService = supplierAdapterServiceFactory.getBeanForPushFromSupplier(supplierType.getCode());
        
        Boolean isSuccess ;
        String failureMsg = "";
        try {
            // 执行外部供应商调用我方实际逻辑
            isSuccess = pushFromSupplierAdapterService.eventDistribution(handleOuterSupplierPushDto);
            // 记日志
            OrderTpiDockingLogUtils.setNormalReturnIntoOrderTpiDockingLogDTO(orderTpiDockingLogDTO, isSuccess, isSuccess);
            orderTpiDockingLogService.updateOrderTpiDockingLog(orderTpiDockingLogDTO);
        } catch (Exception e) {
            OrderTpiDockingLogUtils.setExceptionIntoOrderTpiDockingLog(orderTpiDockingLogDTO, e);
            orderTpiDockingLogService.updateOrderTpiDockingLog(orderTpiDockingLogDTO);
            isSuccess = false;
            failureMsg = e.getMessage();
        }
        
        return Boolean.TRUE.equals(isSuccess) ? RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setSuccess().setData(result) : RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setFailure(failureMsg);
    }

    /**
     * 获取预订单数据
     * @param orderNoList 订单号
     * @param orgCode 机构代码
     * @return 查询数据
     */
    private List<ThirdPartQueryOrderResponseDTO> fetchPreOrderInfo(List<String> orderNoList, String orgCode) {
        PreOrderQueryDTO preOrderQueryDTO = new PreOrderQueryDTO();
        preOrderQueryDTO.setBusiness(PreOrderQueryDTO.BusinessEnum.PRE_ORDER_NUMBER);
        preOrderQueryDTO.setPreOrderNumbers(orderNoList);
        preOrderQueryDTO.setWithDetail(true);
        List<ApplyPreOrderMasterDTO> applyPreOrderMasterDTOList = applicationBusinessRpcClient.listApplyPreOrderMaster(preOrderQueryDTO);
        applyPreOrderMasterDTOList = applyPreOrderMasterDTOList.stream().filter(applyPreOrderMasterDTO -> orgCode.equals(applyPreOrderMasterDTO.getOrgCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyPreOrderMasterDTOList)) {
            return New.emptyList();
        }

        List<Integer> appIdList = applyPreOrderMasterDTOList.stream().map(ApplyPreOrderMasterDTO::getApplicationId).distinct().collect(Collectors.toList());
        List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseRPCClient.findByMasterId(appIdList);
        boolean orgCodeAllMatch = applicationMasterDTOList.stream().allMatch(applicationMasterDTO -> orgCode.equals(applicationMasterDTO.getOrgCode()));
        Preconditions.isTrue(orgCodeAllMatch, "包含不是该单位的订单");
        Map<Long, ApplicationMasterDTO> appIdIdentityMap = DictionaryUtils.toMap(applicationMasterDTOList, ApplicationMasterDTO::getId, Function.identity());

        List<Integer> buyerIdList = applicationMasterDTOList.stream().map(ApplicationMasterDTO::getBuyerId).distinct().collect(Collectors.toList());
        List<UserBaseInfoDTO> buyerInfoList = userRPCClient.getUserByIdsAndOrgId(buyerIdList, applicationMasterDTOList.get(0).getOrgId());
        Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap = DictionaryUtils.toMap(buyerInfoList, UserBaseInfoDTO::getId, Function.identity());

        List<Integer> suppIdList = applyPreOrderMasterDTOList.stream().map(applyPreOrderMasterDTO -> NumberUtils.toInt(applyPreOrderMasterDTO.getSuppId(), -1))
                .filter(suppId -> suppId != -1)
                .distinct().collect(Collectors.toList());
        List<com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> supplierDTOList = suppInfoRPCClient.findSupplierByIds(suppIdList);
        Map<Integer, com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> suppIdIdentityMap;
        Map<String, BankAccountDTO> suppIdBankInfoMap;
        if(CollectionUtils.isNotEmpty(supplierDTOList)){
            suppIdIdentityMap = DictionaryUtils.toMap(supplierDTOList, com.ruijing.shop.crm.api.pojo.dto.SupplierDTO::getId, Function.identity());
            List<BankAccountDTO> suppBankInfoDTOList = suppBankRPCClient.findDefaultBankInfoByIdList(suppIdList);
            suppIdBankInfoMap = DictionaryUtils.toMap(suppBankInfoDTOList, suppBankInfoDTO -> suppBankInfoDTO.getSupplierId().toString(), Function.identity());
        }else {
            suppIdIdentityMap = New.emptyMap();
            suppIdBankInfoMap = New.emptyMap();
        }

        List<ApplyRefFundCardDTO> applyRefFundCardDTOList = applicationBaseRPCClient.getFundCardList(appIdList.stream().map(Integer::longValue).collect(Collectors.toSet()));
        Map<Long, ApplyRefFundCardDTO> appIdCardMap;
        Map<String, FundCardDTO> cardIdIdentityMap;
        if(CollectionUtils.isNotEmpty(applyRefFundCardDTOList)){
            appIdCardMap = DictionaryUtils.toMap(applyRefFundCardDTOList, ApplyRefFundCardDTO::getApplyId, Function.identity());
            List<String> cardIdList = applyRefFundCardDTOList.stream().map(ApplyRefFundCardDTO::getCardId).distinct().collect(Collectors.toList());
            List<FundCardDTO> fundCardDTOList = researchFundCardRPCClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
            cardIdIdentityMap = this.getCardIdIdentityMap(fundCardDTOList);
        }else {
            appIdCardMap = New.emptyMap();
            cardIdIdentityMap = New.emptyMap();
        }

        return applyPreOrderMasterDTOList.stream().map(preOrderMaster -> {
            ApplicationMasterDTO appData = appIdIdentityMap.get(preOrderMaster.getApplicationId().longValue());
            ApplyRefFundCardDTO applyRefFundCardDTO = appIdCardMap.get(appData.getId());
            ThirdPartQueryOrderResponseDTO item = new ThirdPartQueryOrderResponseDTO();
            item.setOrderNo(preOrderMaster.getOrderNumber());
            item.setOrderDate(preOrderMaster.getCreateTime());
            item.setDepartmentName(appData.getBuyDepartmentName());
            item.setBuyerName(appData.getBuyerTrueName());
            item.setReceiveManMobile(appData.getContactPhone());
            item.setDeliveryAddress(appData.getDeliveryAddress());
            item.setOrderDate(appData.getCreateTime());
            item.setStatus(OrderStatusEnum.WaitingForDockingConfirm.getValue());
            item.setPrice(preOrderMaster.getTotalAmount());
            item.setBid(false);
            item.setRemarks(appData.getApplyInfo() != null ? appData.getApplyInfo() : StringUtils.EMPTY);

            if (applyRefFundCardDTO != null) {
                item.setType(applyRefFundCardDTO.getFundType());
                FundCardDTO fundCardDTO = cardIdIdentityMap.get(applyRefFundCardDTO.getCardId());
                if (fundCardDTO != null) {
                    item.setFundProjectCode(fundCardDTO.getFirstLevelCode());
                    item.setFundCardCode(fundCardDTO.getSecondLevelCode());
                    item.setFundSubjectCode(fundCardDTO.getThirdLevelCode());
                }
            }

            com.ruijing.shop.crm.api.pojo.dto.SupplierDTO supplierDTO = suppIdIdentityMap.get(NumberUtils.toInt(preOrderMaster.getSuppId(), -1));
            if (supplierDTO != null) {
                item.setSupplierCode(supplierDTO.getSupplierCode());
                item.setSupplierName(supplierDTO.getSupplierName());
                ContactManDTO contactMan = supplierDTO.getContactMan();
                if(contactMan != null){
                    item.setSupplierContactMan(contactMan.getRealName());
                }
                item.setSupplierTelephone(supplierDTO.getTelephone());
            }

            UserBaseInfoDTO buyerBaseInfoDTO = buyerIdIdentityMap.get(appData.getBuyerId());
            if (buyerBaseInfoDTO != null) {
                item.setBuyerJobNumber(buyerBaseInfoDTO.getJobnumber());
            }

            BankAccountDTO bankAccountDTO = suppIdBankInfoMap.get(preOrderMaster.getSuppId());
            if (bankAccountDTO != null) {
                item.setBankAccount(bankAccountDTO.getBankNumber());
                item.setBank(bankAccountDTO.getBankName());
                item.setBankChild(bankAccountDTO.getBankBranch());
                item.setBankName(bankAccountDTO.getBankAccount());
                item.setBankCode(bankAccountDTO.getBankCode());
            }

            List<ThirdPartQueryOrderDetailResponseDTO> orderDetailList = preOrderMaster.getApplyPreOrderDetails().stream().map(it -> {
                ThirdPartQueryOrderDetailResponseDTO detail = new ThirdPartQueryOrderDetailResponseDTO();
                detail.setBrand(it.getBrandName());
                detail.setQuantity(it.getQuantity());
                detail.setGoodsName(it.getGoodName());
                detail.setGoodsCode(it.getGoodCode());
                detail.setSku(it.getProductCode());
                detail.setSpu(it.getGoodCode());
                detail.setPrice(it.getBidPrice());
                detail.setUnit(it.getUnit());
                detail.setSpecification(it.getSpec());
                detail.setFirstGoodsCategoryId(it.getFirstCategoryId().toString());
                detail.setFirstGoodsCategoryName(it.getFirstCategoryName());
                detail.setSecondCategoryId(it.getSecondCategoryId());
                detail.setSecondCategoryName(it.getSecondCategoryName());
                detail.setCategoryId(it.getCategoryId());
                detail.setCategoryName(it.getClassification());
                return detail;
            }).collect(Collectors.toList());
            item.setOrderDetailList(orderDetailList);
            return item;
        }).collect(Collectors.toList());
    }

    private List<ThirdPartQueryOrderResponseDTO> fetchThirdPartyQueryOrderInfo(List<OrderMasterDTO> orderList, String orgCode) throws ExecutionException, InterruptedException {

        //1.查询订单明细信息
        CompletableFuture<List<OrderDetailDTO>> findOrderDetailTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchOrderDetailInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询订单明细信息" + throwable);
            Cat.logError(CAT_TYPE, "fetOrderDetailInfo", "查询订单明细信息！", throwable);
        }).completable();

        //3.查询发票信息
        CompletableFuture<List<InvoiceDTO>> findInvoiceTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchInvoiceInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询发票信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchInvoiceInfo", "查询发票信息！", throwable);
        }).completable();

        //4.查询供应商信息
        CompletableFuture<List<com.ruijing.shop.crm.api.pojo.dto.SupplierDTO>> findSupplierTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchSupplierInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询供应商信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchSupplierInfo", "查询供应商信息！", throwable);
        }).completable();

        //5.查询供应商资质信息
        CompletableFuture<List<QualificationDTO>> findSupplierQualificationTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchSupplierQualificationInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询供应商资质信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchSupplierQualificationInfo", "查询供应商资质信息！", throwable);
        }).completable();

        //6.查询线下供应商信息
        CompletableFuture<List<OfflineSupplierDTO>> findOffLineSupplierTask = AsyncExecutor.listenableCallAsync(
                () -> {
                    List<Integer> offLineSuppIdList = orderList.stream()
                            .filter(orderMasterDTO -> ProcessSpeciesEnum.OFFLINE.getValue().byteValue() == orderMasterDTO.getSpecies()
                                    && orderMasterDTO.getFsuppid() != null)
                            .map(OrderMasterDTO::getFsuppid).collect(Collectors.toList());
                    return suppInfoRPCClient.getOfflineSuppById(offLineSuppIdList);
                }
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询供应商信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchSupplierInfo", "查询供应商信息！", throwable);
        }).completable();

        //5.查询采购单信息
        CompletableFuture<List<ApplicationMasterDTO>> findApplicationMasterTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchApplicationMasterInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询采购单信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchApplicationMasterInfo", "查询采购单信息！", throwable);
        }).completable();

        //6.查询采购人、收货人信息
        CompletableFuture<List<UserBaseInfoDTO>> findBuyerTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchBuyerInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询采购人信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchBuyerInfo", "查询采购人信息！", throwable);
        }).completable();

        //7.查询供应商开户行信息
        CompletableFuture<List<BankAccountDTO>> findSupplierBankTask = AsyncExecutor.listenableCallAsync(
                () -> suppBankRPCClient.findDefaultBankInfoByIdList(orderList.stream().map(OrderMasterDTO::getFsuppid).collect(Collectors.toList()))
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询供应商开户行信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchSupplierBankInfo", "查询供应商开户行信息！", throwable);
        }).completable();

        // 8.查询批次信息
        CompletableFuture<List<OrderProductBatchesDTO>> batchesTask = AsyncExecutor.listenableCallAsync(
                () -> {
                    if(needOutPutBatchesOrgCodeList.contains(orgCode)){
                        return orderBatchRpcServiceClient.listBatchesByOrderNos(orderList.stream().map(OrderMasterDTO::getForderno).collect(Collectors.toList()));
                    }
                    return New.<OrderProductBatchesDTO>emptyList();
                }
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询批次信息失败" + throwable);
            Cat.logError(CAT_TYPE, "batchesTask", "查询批次信息失败！", throwable);
        }).completable();

        // 9. 查询采购部门管理员信息 key:订单ID value:管理员信息
        CompletableFuture<Map<Integer, UserBaseInfoDTO>> findDeptManagerNameTask = AsyncExecutor.listenableCallAsync(
                () -> {
                    // 查询部门信息和管理员信息
                    List<Long> buyDepartmentIds = orderList.stream().map(OrderMasterDTO::getFbuydepartmentid).map(Long::valueOf).collect(Collectors.toList());
                    List<DepartmentDTO> departmentDTOList = departmentRPCClient.getDepartmentsByIds(buyDepartmentIds).stream().filter(deptDTO -> Objects.nonNull(deptDTO.getManagerId())).collect(Collectors.toList());
                    List<Integer> deptManagerIds = departmentDTOList.stream().map(DepartmentDTO::getManagerId).collect(Collectors.toList());
                    List<UserBaseInfoDTO> userInfoList = userRPCClient.findByUserIdList(deptManagerIds);
                    // 构建管理员ID-管理员信息映射
                    Map<Integer, UserBaseInfoDTO> userInfoMap = New.map();
                    for (UserBaseInfoDTO user : userInfoList) {
                        userInfoMap.put(user.getId(), user);
                    }

                    // 构建部门ID-管理员信息映射
                    Map<Integer, UserBaseInfoDTO> departmentIdToUserInfoMap = New.map();
                    for (DepartmentDTO deptDTO : departmentDTOList) {
                        departmentIdToUserInfoMap.put(deptDTO.getId(), userInfoMap.get(deptDTO.getManagerId()));
                    }

                    // 构建订单ID-管理员信息映射
                    Map<Integer, UserBaseInfoDTO> orderIdToManagerInfoMap = New.map();
                    for (OrderMasterDTO orderMasterDTO : orderList) {
                        orderIdToManagerInfoMap.put(orderMasterDTO.getId(), departmentIdToUserInfoMap.get(orderMasterDTO.getFbuydepartmentid()));
                    }
                    return orderIdToManagerInfoMap;
                }
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询采购部门管理员信息" + throwable);
            Cat.logError(CAT_TYPE, "findDeptManagerNameTask", "查询采购部门管理员信息失败！", throwable);
        }).completable();

        //10.查询订单关联的地址信息
        CompletableFuture<List<OrderAddressDTO>> findOrderAddressTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchOrderAddressInfo(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询订单关联的地址信息" + throwable);
            Cat.logError(CAT_TYPE, "fetchOrderAddressInfo", "查询订单关联的地址信息！", throwable);
        }).completable();

        //11.查询订单关联的额外字段
        CompletableFuture<List<OrderExtraDTO>> findOrderExtraTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchOrderExInfo(orderList, New.list(OrderExtraEnum.REMARK.getValue(), OrderExtraEnum.STATEMENT_WAY_ID.getValue()))
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询订单额外的字段" + throwable);
            Cat.logError(CAT_TYPE, "findOrderExtraTask", "查询订单额外的字段！", throwable);
        }).completable();

        CompletableFuture<List<OrderSupplierRemarkDTO>> findSupplierOrderRemarkTask = AsyncExecutor.listenableCallAsync(
                () -> this.fetchSupplierOrderRemark(orderList)
        ).addFailureCallback(throwable -> {
            LOGGER.error("查询供应商端订单备注" + throwable);
            Cat.logError(CAT_TYPE, "fetchSupplierOrderRemark", "查询供应商端订单备注！", throwable);
        }).completable();

        //并行执行任务
        CompletableFuture.allOf(findOrderDetailTask, findInvoiceTask, findSupplierTask, findSupplierQualificationTask, findOffLineSupplierTask, findApplicationMasterTask,
                findBuyerTask, findSupplierBankTask, batchesTask, findOrderAddressTask, findOrderExtraTask).exceptionally(throwable -> {
            throw new IllegalStateException(throwable);
        }).join();

        List<OrderDetailDTO> orderDetailDTOS = findOrderDetailTask.get();
        List<InvoiceDTO> invoiceDTOS = findInvoiceTask.get();
        List<com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> supplierDTOS = findSupplierTask.get();
        List<ApplicationMasterDTO> applicationMasterDTOS = findApplicationMasterTask.get();
        List<UserBaseInfoDTO> userBaseInfoDTOS = findBuyerTask.get();
        List<BankAccountDTO> suppBankInfoDTOList = findSupplierBankTask.get();
        List<QualificationDTO> qualificationDTOList = findSupplierQualificationTask.get();
        List<OrderAddressDTO> addressDTOList = findOrderAddressTask.get();
        List<OrderExtraDTO> orderExtraDTOList = findOrderExtraTask.get();
        List<OrderSupplierRemarkDTO> orderSupplierRemarkDTOList = findSupplierOrderRemarkTask.get();

        return this.buildThirdPartOrderInfo(orderList,
                orderDetailDTOS,
                invoiceDTOS,
                supplierDTOS,
                findOffLineSupplierTask.get(),
                applicationMasterDTOS,
                userBaseInfoDTOS,
                suppBankInfoDTOList,
                batchesTask.get(),
                findDeptManagerNameTask.get(),
                qualificationDTOList,
                addressDTOList,
                orderExtraDTOList,
                orderSupplierRemarkDTOList);
    }


    /**
     * 获得订单详细信息
     *
     * @param orderList
     * @return
     */
    private List<OrderDetailDTO> fetchOrderDetailInfo(List<OrderMasterDTO> orderList) {
        List<Integer> orderIdList = orderList.stream().map(OrderMasterDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        return orderDetailRPCClient.findOrderDetailByMasterId(orderIdList);
    }

    /**
     * 获得订单关联的发票信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<InvoiceDTO> fetchInvoiceInfo(List<OrderMasterDTO> orderSearchInfo) {
        List<Integer> orderIdList = orderSearchInfo.stream().map(OrderMasterDTO::getId).collect(Collectors.toList());
        Integer orgId = orderSearchInfo.get(0).getFuserid();
        return invoiceRPCClient.findInvoiceByOrderIdList(orderIdList, orgId);
    }

    /**
     * 获取与订单关联的供应商信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> fetchSupplierInfo(List<OrderMasterDTO> orderSearchInfo) {
        List<Integer> supplierIdList = orderSearchInfo.stream()
                .filter(it -> it.getFsuppid() != null && ProcessSpeciesEnum.NORMAL.getValue().byteValue() == it.getSpecies())
                .map(OrderMasterDTO::getFsuppid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return Collections.emptyList();
        }
        return suppInfoRPCClient.findSupplierByIds(supplierIdList);
    }

    /**
     * 获取供应商资质信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<QualificationDTO> fetchSupplierQualificationInfo(List<OrderMasterDTO> orderSearchInfo){
        List<Integer> supplierIdList = orderSearchInfo.stream()
            .filter(it -> it.getFsuppid() != null && ProcessSpeciesEnum.NORMAL.getValue().byteValue() == it.getSpecies())
            .map(OrderMasterDTO::getFsuppid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return Collections.emptyList();
        }
        return suppInfoRPCClient.getQualificationList(supplierIdList);
    }

    /**
     * 获取与订单关联的地址信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<OrderAddressDTO> fetchOrderAddressInfo(List<OrderMasterDTO> orderSearchInfo){
        List<Integer> orderIdList = orderSearchInfo.stream()
                .map(OrderMasterDTO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyList();
        }
        return orderAddressRpcClient.findByOrderId(orderIdList);
    }


    /**
     * 获取订单额外的字段
     *
     * @param orderSearchInfo
     * @param extraKeyList
     * @return
     */
    private List<OrderExtraDTO> fetchOrderExInfo(List<OrderMasterDTO> orderSearchInfo, List<Integer> extraKeyList){
        List<Integer> orderIdList = orderSearchInfo.stream()
                .map(OrderMasterDTO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyList();
        }
        if(CollectionUtils.isEmpty(extraKeyList)){
            return Collections.emptyList();
        }
        return orderExtraClient.selectByOrderIdAndExtraKey(orderIdList, extraKeyList);
    }

    /**
     * 查询供应商端订单备注
     *
     * @param orderSearchInfo
     * @return
     */
    private List<OrderSupplierRemarkDTO> fetchSupplierOrderRemark(List<OrderMasterDTO> orderSearchInfo){
        List<Integer> orderIdList = orderSearchInfo.stream()
                .map(OrderMasterDTO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyList();
        }
        return orderRelatedRPCClient.findSupplierRemarkByOrderIdList(orderIdList);
    }
    /**
     * 获取与订单关联的采购单信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<ApplicationMasterDTO> fetchApplicationMasterInfo(List<OrderMasterDTO> orderSearchInfo) {
        List<Integer> buyAppIdList = orderSearchInfo.stream().map(OrderMasterDTO::getFtbuyappid).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(buyAppIdList)){
            return New.emptyList();
        }
        return applicationBaseRPCClient.findByMasterId(buyAppIdList);
    }

    /**
     * 获得与订单关联的采购人和收货人信息
     *
     * @param orderSearchInfo
     * @return
     */
    private List<UserBaseInfoDTO> fetchBuyerInfo(List<OrderMasterDTO> orderSearchInfo) {
        List<Integer> buyerIdList = orderSearchInfo.stream().map(OrderMasterDTO::getFbuyerid).collect(Collectors.toList());
        List<Integer> acceptorIdList = orderSearchInfo.stream().filter(it -> it.getFlastreceivemanid() != null).map(it -> Integer.parseInt(it.getFlastreceivemanid())).collect(Collectors.toList());
        Integer orgId = orderSearchInfo.get(0).getFuserid();
        if (CollectionUtils.isEmpty(buyerIdList) || orgId == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isNotEmpty(acceptorIdList)) {
            buyerIdList.addAll(acceptorIdList);
        }
        return userRPCClient.getUserByIdsAndOrgId(buyerIdList, orgId);
    }

    /**
     * 构建TPI查询订单出参
     *
     * @param orderList             订单主表信息
     * @param invoiceDTOS           汇总发票信息
     * @param supplierDTOS          供应商信息
     * @param applicationMasterDTOS 采购单信息
     * @return TPI订单结果
     */
    public List<ThirdPartQueryOrderResponseDTO> buildThirdPartOrderInfo(List<OrderMasterDTO> orderList, List<OrderDetailDTO> orderDetailDTOS,
                                                                        List<InvoiceDTO> invoiceDTOS, List<com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> supplierDTOS,
                                                                        List<OfflineSupplierDTO> offlineSupplierDTOList,
                                                                        List<ApplicationMasterDTO> applicationMasterDTOS, List<UserBaseInfoDTO> userBaseInfoDTOS,
                                                                        List<BankAccountDTO> suppBankInfoDTOList,
                                                                        List<OrderProductBatchesDTO> orderUniqueBarCodeDTOList,
                                                                        Map<Integer, UserBaseInfoDTO> orderIdToManagerInfoMap,
                                                                        List<QualificationDTO> supplierQualificationDTOList,
                                                                        List<OrderAddressDTO> addressDTOList,
                                                                        List<OrderExtraDTO> orderExtraDTOList,
                                                                        List<OrderSupplierRemarkDTO> orderSupplierRemarkDTOList) {
        Map<Integer, List<OrderDetailDTO>> orderIdDetailMap = orderDetailDTOS.stream().collect(Collectors.groupingBy(OrderDetailDTO::getFmasterid));
        Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap = DictionaryUtils.toMap(userBaseInfoDTOS, UserBaseInfoDTO::getId, Function.identity());
        Map<Integer, BankAccountDTO> suppIdBankInfoMap = DictionaryUtils.toMap(suppBankInfoDTOList, BankAccountDTO::getSupplierId, Function.identity());
        Map<String, List<OrderProductBatchesDTO>> orderNoBatchesMap = DictionaryUtils.groupBy(orderUniqueBarCodeDTOList, OrderProductBatchesDTO::getOrderNo);
        Map<Integer, List<QualificationDTO>> suppId2QualificationMap = DictionaryUtils.groupBy(supplierQualificationDTOList, QualificationDTO::getSuppId);
        Map<Integer, OrderAddressDTO> orderId2AddressMap = DictionaryUtils.toMap(addressDTOList, OrderAddressDTO::getId, Function.identity());
        Map<Integer, List<OrderSupplierRemarkDTO>> orderId2SupplierRemark = DictionaryUtils.groupBy(orderSupplierRemarkDTOList, OrderSupplierRemarkDTO::getOrderId);
        Map<Integer, Map<Integer, String>> orderId2ExtraKeyValMap = New.map();
        for(OrderExtraDTO orderExtraDTO : orderExtraDTOList){
            orderId2ExtraKeyValMap.computeIfAbsent(orderExtraDTO.getOrderId(), (k)->New.map()).put(orderExtraDTO.getExtraKey(), orderExtraDTO.getExtraValue());
        }
        Map<Integer, Boolean> orderIdUnifiedSettlementMap = this.getOrderIdIsUnifiedSettlementMap(orderList.get(0).getFusercode(), orderList, orderId2ExtraKeyValMap);

        List<GoodsReturnDTO> goodsReturnDTOList = goodsReturnRPCClient.findByOrderIdList(New.list(orderIdDetailMap.keySet()));
        Map<Integer, List<GoodsReturnDTO>> orderIdReturnListMap = DictionaryUtils.groupBy(goodsReturnDTOList, GoodsReturnDTO::getOrderId);
        //所有层级的经费卡id -> 经费卡实例的字典
        Map<String, FundCardDTO> cardIdIdentityMap;
        Map<Integer, String> orderIdCardMap;
        List<RefFundcardOrderDTO> refFundcardOrderDTOList = orderRelatedRPCClient.findRefFundCardByOrderIdList(orderList.stream().map(OrderMasterDTO::getId).collect(Collectors.toList()));
        if(CollectionUtils.isNotEmpty(refFundcardOrderDTOList)){
            List<String> cardIdList = refFundcardOrderDTOList.stream().map(RefFundcardOrderDTO::getCardId).distinct().collect(Collectors.toList());
            orderIdCardMap = DictionaryUtils.toMap(refFundcardOrderDTOList, card -> Integer.valueOf(card.getOrderId()), RefFundcardOrderDTO::getCardId);
            List<FundCardDTO> fundCardDTOList = researchFundCardRPCClient.findAllCardByOrgCodeAndCardId(orderList.get(0).getFusercode(), cardIdList);
            cardIdIdentityMap = this.getCardIdIdentityMap(fundCardDTOList);
        }else {
            cardIdIdentityMap = New.emptyMap();
            orderIdCardMap = New.emptyMap();
        }

        Map<Integer, com.ruijing.shop.crm.api.pojo.dto.SupplierDTO> suppIdIdentityMap = CollectionUtils.isEmpty(supplierDTOS) ? New.emptyMap() : DictionaryUtils.toMap(supplierDTOS, com.ruijing.shop.crm.api.pojo.dto.SupplierDTO::getId, Function.identity());
        Map<Integer, OfflineSupplierDTO> offLineSuppIdIdentityMap = DictionaryUtils.toMap(offlineSupplierDTOList, it -> it.getId().intValue(), Function.identity());
        Map<Integer, ApplicationMasterDTO> appMasterIdIdentityMap = DictionaryUtils.toMap(applicationMasterDTOS, it -> it.getId().intValue(), Function.identity());
        Map<Integer, List<InvoiceDTO>> orderIdInvoiceMap = new HashMap<>(orderList.size());
        for (InvoiceDTO invoiceDTO : invoiceDTOS) {
            List<InvoiceRefResultDTO> invoiceRefDTOList = invoiceDTO.getInvoiceRefDTOS();
            if (CollectionUtils.isEmpty(invoiceDTOS)) {
                continue;
            }
            for (InvoiceRefResultDTO invoiceRefDTO : invoiceRefDTOList) {
                int orderId = invoiceRefDTO.getSourceId().intValue();
                List<InvoiceDTO> existInvoiceList = orderIdInvoiceMap.get(orderId);
                if (CollectionUtils.isNotEmpty(existInvoiceList)) {
                    existInvoiceList.add(invoiceDTO);
                } else {
                    orderIdInvoiceMap.put(orderId, New.list(invoiceDTO));
                }
            }
        }
        List<ThirdPartQueryOrderResponseDTO> result = orderList.stream().map(order -> {
            ThirdPartQueryOrderResponseDTO item = new ThirdPartQueryOrderResponseDTO();
            item.setOrderId(order.getId());
            item.setOrderNo(order.getForderno());
            if(OrderCommonUtils.isSplitOrder(order.getForderno())){
                item.setParentOrderNo(order.getForderno().substring(0, order.getForderno().length() - 1));
            }
            List<InvoiceDTO> invoiceDTOList = orderIdInvoiceMap.get(order.getId());
            if (CollectionUtils.isNotEmpty(invoiceDTOList)) {
                item.setSummaryNo(invoiceDTOList.get(0).getSummaryNo());
                List<ThirdPartQueryOrderInvoiceResponseDTO> invoiceCollect = invoiceDTOList.stream().map(invoiceDTO -> new ThirdPartQueryOrderInvoiceResponseDTO()
                        .setInvoiceNo(invoiceDTO.getInvoiceNo())
                        .setInvoiceUrl(invoiceDTO.getInvoicePhoto())
                        .setInvoiceDate(invoiceDTO.getInvoiceDate())
                        .setInvoiceRemark(invoiceDTO.getRemark())
                        .setBankCode(invoiceDTO.getBankCode())
                        .setBankNameCompany(invoiceDTO.getBankNameCompany())
                        .setBankNum(invoiceDTO.getBankNum())
                        .setBankName(invoiceDTO.getBankName())
                ).collect(Collectors.toList());
                item.setInvoiceList(invoiceCollect);
            }
            if(order.getFuserid().equals(OrgEnum.SHEN_ZHEN_SHI_REN_MIN_YI_YUAN.getValue())){
                //对方的课题号
                ApplyMasterExtensionQueryDTO request = new ApplyMasterExtensionQueryDTO();
                request.setApplyIds(New.list(order.getFtbuyappid()));
                List<ApplyMasterExtensionDTO> applyMasterExtensionDTOList = applicationBaseRPCClient.findExtensionByApplicationId(request);
                if(CollectionUtils.isNotEmpty(applyMasterExtensionDTOList) && Objects.nonNull(applyMasterExtensionDTOList.get(0).getExtraInfo())){
                    item.setTopicNumber(applyMasterExtensionDTOList.get(0).getExtraInfo().getTopicNumber());
                }

                List<UserExtendInfoDTO> userExtraInfo = userRPCClient.getUserExtraInfo(New.list(order.getFbuyerid()), order.getFuserid(), UserExtendInfoTypeEnum.DEPT_USER_INFO);
                if(CollectionUtils.isNotEmpty(userExtraInfo)){
                    item.setDepartmentName(userExtraInfo.get(0).getInfo());
                }
            }else {
                item.setDepartmentName(order.getFbuydepartment());
            }
            item.setBuyerName(order.getFbuyername());
            item.setBuyerTelephone(order.getFbuyertelephone());
            item.setBuyerEmail(order.getFbuyeremail());
            item.setOrderDate(order.getForderdate());
            item.setStatus(order.getStatus());
            item.setDeliveryAddress(order.getFbiderdeliveryplace());
            item.setReceiveManId(order.getFlastreceivemanid() != null ? Integer.parseInt(order.getFlastreceivemanid()) : null);
            item.setReceiveMan(order.getFlastreceiveman());
            item.setReceiveManMobile(order.getFbuyertelephone());
            item.setReceiveDate(order.getFlastreceivedate());
            item.setPrice(order.getForderamounttotal());
            //这两个字段暂时为空
            item.setApproval(StringUtils.EMPTY);
            item.setApprovalDate(null);
            item.setFreightAmount(order.getCarryFee());

            // 计算优惠(下单时候的优惠，不考虑退货后的结果。如果不显示原价，这里为0)
            BigDecimal originalAmount = order.getOriginalAmount();
            BigDecimal discountAmount = originalAmount.subtract(order.getForderamounttotal());
            item.setDiscountAmount(discountAmount.compareTo(BigDecimal.ZERO) > 0 ? discountAmount : BigDecimal.ZERO);

            if (StringUtils.isNotBlank(order.getReceivePicUrls())) {
                String[] photos = order.getReceivePicUrls().split(";");
                item.setOrderAcceptPhotoList(Arrays.asList(photos));
            }

            if(ProcessSpeciesEnum.NORMAL.getValue().byteValue() == order.getSpecies()){
                com.ruijing.shop.crm.api.pojo.dto.SupplierDTO supplierDTO = suppIdIdentityMap.get(order.getFsuppid());
                List<QualificationDTO> qualificationDTOS = suppId2QualificationMap.get(order.getFsuppid());
                if (supplierDTO != null) {
                    item.setSupplierCode(supplierDTO.getSupplierCode());
                    item.setSupplierName(supplierDTO.getSupplierName());
                    ContactManDTO contactMan = supplierDTO.getContactMan();
                    if(contactMan != null){
                        item.setSupplierContactMan(contactMan.getRealName());
                        item.setSupplierEmail(contactMan.getEmail());
                    }
                    item.setSupplierTelephone(supplierDTO.getTelephone());
                }
                //资质信息
                if(CollectionUtils.isNotEmpty(qualificationDTOS)){
                    item.setUnifyCode(qualificationDTOS.get(0).getUnifyCode());
                }
            }else {
                OfflineSupplierDTO offlineSupplierDTO = offLineSuppIdIdentityMap.get(order.getFsuppid());
                if(offlineSupplierDTO != null){
                    item.setSupplierCode(offlineSupplierDTO.getSupplierCode());
                    item.setSupplierName(offlineSupplierDTO.getSupplierName());
                    item.setSupplierContactMan(offlineSupplierDTO.getContactMan());
                    item.setSupplierEmail(offlineSupplierDTO.getEmail());
                    item.setSupplierTelephone(offlineSupplierDTO.getTelephone());
                    item.setUnifyCode(offlineSupplierDTO.getUnifyCode());
                }
            }
            item.setBid(OrderTypeEnum.BID_ORDER.getCode().equals(order.getOrderType()));

            FundCardDTO fundCardDTO = cardIdIdentityMap.get(orderIdCardMap.get(order.getId()));
            if (fundCardDTO != null) {
                item.setFundProjectCode(fundCardDTO.getFirstLevelCode());
                item.setFundCardCode(fundCardDTO.getSecondLevelCode());
                item.setFundSubjectCode(fundCardDTO.getThirdLevelCode());
            }

            item.setType(order.getFundTypeName() != null ? NumberUtils.toInt(order.getFundTypeName(), -1) : null);
            item.setRemarks(appMasterIdIdentityMap.get(order.getFtbuyappid()) != null ? appMasterIdIdentityMap.get(order.getFtbuyappid()).getApplyInfo() : StringUtils.EMPTY);
            List<OrderDetailDTO> orderDetailItem = orderIdDetailMap.get(order.getId());
            if (CollectionUtils.isEmpty(orderDetailItem)) {
                return item;
            }

            List<OrderProductBatchesDTO> matchBatches = orderNoBatchesMap.get(order.getForderno());
            if(needOutPutBatchesOrgCodeList.contains(order.getFusercode()) && CollectionUtils.isNotEmpty(matchBatches)){
                // 江西肿瘤，按商品id-批次-有效期-生产厂家聚合商品。没数据了就按正常返回
                item.setOrderDetailList(this.groupByDetailBatches(matchBatches, orderDetailItem, orderIdReturnListMap.get(order.getId())));
            }else {
                List<ThirdPartQueryOrderDetailResponseDTO> orderDetailList = orderDetailItem.stream().map(this::orderDetailDto2QryDto).collect(Collectors.toList());
                item.setOrderDetailList(orderDetailList);
            }

            UserBaseInfoDTO buyerBaseInfoDTO = buyerIdIdentityMap.get(order.getFbuyerid());
            if (buyerBaseInfoDTO != null) {
                item.setBuyerJobNumber(buyerBaseInfoDTO.getJobnumber());
            }
            UserBaseInfoDTO acceptorBaseInfoDTO = buyerIdIdentityMap.get(NumberUtils.toInt(order.getFlastreceivemanid()));
            if (acceptorBaseInfoDTO != null) {
                item.setAcceptorJobNumber(acceptorBaseInfoDTO.getJobnumber());
            }

            BankAccountDTO suppBankInfoDTO = suppIdBankInfoMap.get(order.getFsuppid());
            if (suppBankInfoDTO != null) {
                item.setBankAccount(suppBankInfoDTO.getBankNumber());
                item.setBank(suppBankInfoDTO.getBankName());
                item.setBankChild(suppBankInfoDTO.getBankBranch());
                item.setBankName(suppBankInfoDTO.getBankAccount());
                item.setBankCode(suppBankInfoDTO.getBankCode());
            }

            // 填充管理员信息
            UserBaseInfoDTO managerInfo = orderIdToManagerInfoMap.get(order.getId());
            if (Objects.nonNull(managerInfo)) {
                item.setDeptManagerName(managerInfo.getName());
            }

            //收货地址信息
            OrderAddressDTO orderAddressDTO = orderId2AddressMap.get(order.getId());
            if(Objects.nonNull(orderAddressDTO)){
                item.setProvince(orderAddressDTO.getProvince());
                item.setCity(orderAddressDTO.getCity());
                item.setRegion(orderAddressDTO.getRegion());
            }

            //采购人订单备注
            if(orderId2ExtraKeyValMap.get(order.getId()) != null){
                item.setBuyerOrderRemark(orderId2ExtraKeyValMap.get(order.getId()).get(OrderExtraEnum.REMARK.getValue()));
            }

            //供应商端订单备注
            List<OrderSupplierRemarkDTO> orderSupplierRemarkDTOS = orderId2SupplierRemark.get(order.getId());
            if(CollectionUtils.isNotEmpty(orderSupplierRemarkDTOS)){
                item.setSupplierOrderRemark(orderSupplierRemarkDTOS.stream().map(OrderSupplierRemarkDTO::getRemark).collect(Collectors.joining(",")));
            }

            item.setUnifiedSettlement(orderIdUnifiedSettlementMap.get(order.getId()));
            return item;
        }).collect(Collectors.toList());

        return result;
    }

    /**
     * 获取订单id-统一结算映射
     * @param orgCode 机构编码
     * @param orderList 订单列表
     * @param orderIdKeyValMap 订单id-extraKV映射
     */
    private Map<Integer, Boolean> getOrderIdIsUnifiedSettlementMap(String orgCode, List<OrderMasterDTO> orderList, Map<Integer, Map<Integer, String>> orderIdKeyValMap){
        if(!OrgEnum.GUANG_ZHOU_SHI_YAN_SHI.getCode().equals(orgCode)){
            return New.emptyMap();
        }

        Map<String, Boolean> statementWayIdUnifiedSettlementMap = New.map();
        if(orderIdKeyValMap != null && !orderIdKeyValMap.isEmpty()){
            // 获取单据的统一结算配置
            Set<Integer> statementWayIdSet = New.set();
            for(Map<Integer, String> itemMap : orderIdKeyValMap.values()){
                String statementWayIdVal = itemMap.get(OrderExtraEnum.STATEMENT_WAY_ID.getValue());
                if(statementWayIdVal != null){
                    statementWayIdSet.add(Integer.parseInt(statementWayIdVal));
                }
            }
            if (CollectionUtils.isNotEmpty(statementWayIdSet)) {
                List<StatementWayConfigDTO> statementWayConfigDTOList = researchStatementRPCClient.listStatementWayConfigByIds(New.list(statementWayIdSet));
                if (CollectionUtils.isNotEmpty(statementWayConfigDTOList)) {
                    for (StatementWayConfigDTO statementWayConfigDTO : statementWayConfigDTOList) {
                        String statementWayId = statementWayConfigDTO.getId().toString();
                        // 根据名称判断是否是集中结算 广州实验室定制
                        String unifiedSettlementWayName = "集中结算";
                        Boolean isUnifiedSettlement = unifiedSettlementWayName.equals(statementWayConfigDTO.getName());
                        statementWayIdUnifiedSettlementMap.put(statementWayId, isUnifiedSettlement);
                    }
                }
            }
        }


        // 有订单特殊结算方式取结算方式，没有默认走集中结算
        Map<Integer, Boolean> orderIdOrgUnifiedSettlementMap = New.map();
        for(OrderMasterDTO orderMasterDTO : orderList){
            Integer orderId = orderMasterDTO.getId();
            // 默认走集中结算
            orderIdOrgUnifiedSettlementMap.put(orderId, true);
            if(orderIdKeyValMap != null && orderIdKeyValMap.get(orderId) != null){
                String statementWayIdStr = orderIdKeyValMap.get(orderId).get(OrderExtraEnum.STATEMENT_WAY_ID.getValue());
                if(statementWayIdStr != null){
                    orderIdOrgUnifiedSettlementMap.put(orderId, statementWayIdUnifiedSettlementMap.get(statementWayIdStr));
                }
            }
        }
        return orderIdOrgUnifiedSettlementMap;
    }


    private Map<String, FundCardDTO> getCardIdIdentityMap(List<FundCardDTO> fundCardDTOList) {
        //所有层级的经费卡id -> 经费卡实例的字典
        Map<String, FundCardDTO> cardIdIdentityMap = New.map();
        for (FundCardDTO fundCardDTO : fundCardDTOList) {
            cardIdIdentityMap.put(fundCardDTO.getId(), fundCardDTO);
            List<FundCardDTO> secondFundCards = fundCardDTO.getFundCardDTOs();
            if (CollectionUtils.isEmpty(secondFundCards)) {
                continue;
            }
            FundCardDTO secondFundCard = secondFundCards.get(0);
            cardIdIdentityMap.put(secondFundCard.getId(), secondFundCard);
            List<FundCardDTO> thirdFundCards = secondFundCard.getFundCardDTOs();
            if (CollectionUtils.isEmpty(thirdFundCards)) {
                continue;
            }
            FundCardDTO thirdFundCard = thirdFundCards.get(0);
            cardIdIdentityMap.put(thirdFundCard.getId(), thirdFundCard);
        }
        return cardIdIdentityMap;
    }

    /**
     * 转换外部数据为内部传输体
     *
     * @param outerSupplierRequestDTO 外部供应商调用的内部传输体
     * @return List<HandleOuterSupplierPushDTO>
     */
    private List<HandleOuterSupplierPushDTO> getHandleOuterSupplierPushDtoList(OuterSupplierRequestDTO outerSupplierRequestDTO) {
        List<OuterSupplierOrderDTO> outerSupplierOrderDTOList = outerSupplierRequestDTO.getOuterSupplierOrderDTOList();
        List<HandleOuterSupplierPushDTO> handleOuterSupplierPushDtoList = new ArrayList<>(outerSupplierOrderDTOList.size());
        String supplierCode = outerSupplierRequestDTO.getSupplierCode();
        SupplierEventTypeEnum supplierEventTypeEnum = outerSupplierRequestDTO.getSupplierEventTypeEnum();
        for (OuterSupplierOrderDTO outerSupplierOrderDTO : outerSupplierOrderDTOList) {
            HandleOuterSupplierPushDTO handleOuterSupplierPushDTO = new HandleOuterSupplierPushDTO();
            handleOuterSupplierPushDTO.setOrderNo(outerSupplierOrderDTO.getOrderNo());
            handleOuterSupplierPushDTO.setSupplierCode(supplierCode);
            handleOuterSupplierPushDTO.setLogisticsInformationDTO(outerSupplierOrderDTO.getLogisticsInformationDTO());
            handleOuterSupplierPushDTO.setCancelReason(outerSupplierOrderDTO.getCancelReason());
            handleOuterSupplierPushDTO.setSupplierEventTypeEnum(supplierEventTypeEnum);
            handleOuterSupplierPushDtoList.add(handleOuterSupplierPushDTO);
        }
        return handleOuterSupplierPushDtoList;
    }

    /**
     * 按批次聚合商品详情，江西肿瘤才用到
     * @param matchBatches 批次信息
     * @param detailList 商品详情
     * @return 商品详情粒度数据
     */
    private List<ThirdPartQueryOrderDetailResponseDTO> groupByDetailBatches(List<OrderProductBatchesDTO> matchBatches, List<OrderDetailDTO> detailList, List<GoodsReturnDTO> goodsReturnDTOList) {
        if (CollectionUtils.isNotEmpty(matchBatches)) {
            Map<Integer, OrderDetailDTO> detailIdDetailMap = DictionaryUtils.toMap(detailList, OrderDetailDTO::getId, Function.identity());
            Map<Integer, Integer> detailIdReturnCountMap = New.map();
            if(CollectionUtils.isNotEmpty(goodsReturnDTOList)){
                for (GoodsReturnDTO goodsReturnDTO : goodsReturnDTOList) {
                    if(GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturnDTO.getGoodsReturnStatus())){
                        for (GoodsReturnDetailDTO goodsReturnDetailDTO : goodsReturnDTO.getGoodsReturnDetailDTOS()) {
                            Integer returnCount = detailIdReturnCountMap.getOrDefault(goodsReturnDetailDTO.getDetailId(), 0);
                            detailIdReturnCountMap.put(goodsReturnDetailDTO.getDetailId(), returnCount + Integer.parseInt(goodsReturnDetailDTO.getQuantity()));
                        }
                    }
                }
            }
            List<ThirdPartQueryOrderDetailResponseDTO> resultList = New.listWithCapacity(matchBatches.size());
            for (OrderProductBatchesDTO batchItem : matchBatches) {
                OrderDetailDTO matchDetail = detailIdDetailMap.get(batchItem.getOrderDetailId());
                if (matchDetail == null) {
                    continue;
                }
                ThirdPartQueryOrderDetailResponseDTO batchDetail = this.orderDetailDto2QryDto(matchDetail);
                batchDetail.setBatches(batchItem.getBatchNumber())
                        .setExpiration(DateUtils.format("yyyy-MM-dd", batchItem.getExpiration()))
                        .setManufacturer(batchItem.getManufacturer())
                        .setQuantity(BigDecimal.valueOf(batchItem.getQuantity()));
                batchDetail.setCancelQuantity(BigDecimal.valueOf(detailIdReturnCountMap.getOrDefault(batchItem.getOrderDetailId(), 0)));
                batchDetail.setBidAmount(batchDetail.getPrice().multiply(batchDetail.getQuantity()));
                resultList.add(batchDetail);
            }
            return resultList;
        }
        return New.emptyList();
    }

    private ThirdPartQueryOrderDetailResponseDTO orderDetailDto2QryDto(OrderDetailDTO it){
        ThirdPartQueryOrderDetailResponseDTO detail = new ThirdPartQueryOrderDetailResponseDTO();
        detail.setBrand(it.getFbrand());
        detail.setQuantity(it.getFquantity());
        detail.setGoodsName(it.getFgoodname());
        detail.setGoodsCode(it.getFgoodcode());
        detail.setSku(it.getProductCode());
        detail.setSpu(it.getFgoodcode());
        detail.setPrice(it.getFbidprice());
        detail.setBidAmount(it.getFbidamount());
        detail.setUnit(it.getFunit());
        detail.setCancelQuantity(it.getFcancelquantity());
        detail.setReturnStatus(it.getReturnStatus());
        detail.setSpecification(it.getFspec());
        detail.setFirstGoodsCategoryId(it.getFirstCategoryId().toString());
        detail.setFirstGoodsCategoryName(it.getFirstCategoryName());
        detail.setSecondCategoryId(it.getSecondCategoryId());
        detail.setSecondCategoryName(it.getSecondCategoryName());
        detail.setCategoryId(it.getCategoryid());
        detail.setCategoryName(it.getFclassification());
        detail.setCategoryTag(it.getCategoryTag());
        return detail;
    }
}
