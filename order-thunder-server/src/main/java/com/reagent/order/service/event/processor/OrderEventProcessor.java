package com.reagent.order.service.event.processor;

import com.reagent.order.config.docking.event.enums.OrderEvent;
import com.reagent.order.dto.OrderPushDTO;

import java.util.List;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2024-06-20 14:38
 * @description: 订单事件监听者
 * 如果需要指定执行顺序请使用 {@link org.springframework.core.annotation.Order}，序号小的先执行，默认执行序号为5。
 * 通常仅有throwException返回true的才需要关注：保证在可能抛出异常情况下的执行顺序
 */
public interface OrderEventProcessor {

    int DEFAULT_ORDER = 5;

    /**
     * 通知订单事件发生
     * @param orderPushDTO 订单变更数据
     * @param orderEventList 订单事件
     */
    void notice(OrderPushDTO orderPushDTO, List<OrderEvent> orderEventList);

    /**
     * 是否抛出异常
     * @return 是否需要抛出异常
     */
    default boolean throwException(){
        return false;
    }
}
