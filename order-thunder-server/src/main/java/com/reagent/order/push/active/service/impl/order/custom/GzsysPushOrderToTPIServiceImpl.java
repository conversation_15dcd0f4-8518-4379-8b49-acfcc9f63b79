package com.reagent.order.push.active.service.impl.order.custom;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.order.push.active.PushToTPIConstant;
import com.reagent.order.push.active.service.impl.order.PushOrderToTPIServiceImpl;
import com.reagent.order.rpc.OrderExtraClient;
import com.reagent.order.rpc.ResearchStatementRPCClient;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.statement.api.statement.dto.StatementWayConfigDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2025-06-05 10:29
 * @description:
 */
@Service(OrgConst.GUANG_ZHOU_SHI_YAN_SHI + PushToTPIConstant.PUSH_ORDER_SERVICE)
public class GzsysPushOrderToTPIServiceImpl extends PushOrderToTPIServiceImpl {

    @Resource
    private ResearchStatementRPCClient researchStatementRPCClient;

    @Resource
    private OrderExtraClient orderExtraClient;
    
    /**
     * 结算配置-集中结算名称（广州实验室定制）
     */
    private static final String UNIFIED_SETTLEMENT_NAME = "集中结算";

    @Override
    protected List<ExtraDTO> getPushOrderMasterExtraDTO(OrderMasterDTO orderMasterDTO, OrderPushEventEnum eventType) {
        List<ExtraDTO> extraDTOList = super.getPushOrderMasterExtraDTO(orderMasterDTO, eventType);

        Boolean isUnifiedSettlement = getOrderUnifiedSettlement(orderMasterDTO.getId());
        extraDTOList.add(new ExtraDTO("unifiedSettlement", isUnifiedSettlement.toString()));
        return extraDTOList;
    }

    /**
     * 获取订单是否为集中结算
     * @param orderId 订单ID
     * @return true-集中结算，false-自结算
     */
    private Boolean getOrderUnifiedSettlement(Integer orderId) {
        List<OrderExtraDTO> orderExtraList = orderExtraClient.selectByOrderIdAndExtraKey(
            New.list(orderId), New.list(OrderExtraEnum.STATEMENT_WAY_ID.getValue()));

        // 查不到单据配置快照，默认走集中结算
        if(CollectionUtils.isEmpty(orderExtraList)){
            return true;
        }

        Integer statementWayId = Integer.valueOf(orderExtraList.get(0).getExtraValue());
        List<StatementWayConfigDTO> configList = researchStatementRPCClient.listStatementWayConfigByIds(New.list(statementWayId));

        // 查不到对应的OMS结算方式配置，默认走集中结算
        if(CollectionUtils.isEmpty(configList)){
            return true;
        }

        // 根据配置名称判断是否为集中结算
        return UNIFIED_SETTLEMENT_NAME.equals(configList.get(0).getName());
    }

}
