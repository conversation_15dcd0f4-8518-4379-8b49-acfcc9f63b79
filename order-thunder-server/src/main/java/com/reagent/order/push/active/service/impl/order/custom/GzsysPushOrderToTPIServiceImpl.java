package com.reagent.order.push.active.service.impl.order.custom;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.order.push.active.PushToTPIConstant;
import com.reagent.order.push.active.service.impl.order.PushOrderToTPIServiceImpl;
import com.reagent.order.rpc.OrderExtraClient;
import com.reagent.order.rpc.ResearchStatementRPCClient;
import com.reagent.order.rpc.oms.OmsSysClient;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.statement.api.statement.dto.StatementWayConfigDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2025-06-05 10:29
 * @description:
 */
@Service(OrgConst.GUANG_ZHOU_SHI_YAN_SHI + PushToTPIConstant.PUSH_ORDER_SERVICE)
public class GzsysPushOrderToTPIServiceImpl extends PushOrderToTPIServiceImpl {

    @Resource
    private ResearchStatementRPCClient researchStatementRPCClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OmsSysClient omsSysClient;

    /**
     * 结算配置-集中结算名称（定制 根据配置名称判断是否是集中结算）
     */
    private final static String unifiedSettlementWayName = "集中结算";

    @Override
    protected List<ExtraDTO> getPushOrderMasterExtraDTO(OrderMasterDTO orderMasterDTO, OrderPushEventEnum eventType) {
        List<ExtraDTO> extraDTOList = super.getPushOrderMasterExtraDTO(orderMasterDTO, eventType);
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderMasterDTO.getId()), New.list(OrderExtraEnum.STATEMENT_WAY_ID.getValue()));
        Boolean isUnifiedSettlement = null;
        if(CollectionUtils.isNotEmpty(orderExtraDTOList)){
            Integer statementWayId = Integer.valueOf(orderExtraDTOList.get(0).getExtraValue());
            List<StatementWayConfigDTO> statementWayConfigDTOList = researchStatementRPCClient.listStatementWayConfigByIds(New.list(statementWayId));
            if(CollectionUtils.isNotEmpty(statementWayConfigDTOList)){
                // 先取单据级别的结算方式,定制根据名称判断是否是集中结算
                isUnifiedSettlement = unifiedSettlementWayName.equals(statementWayConfigDTOList.get(0).getName());
            }
        }
        if(isUnifiedSettlement == null){
            // 没有单据级别的结算方式，则取单位级别的
            List<BaseConfigDTO> baseConfigDTOList = omsSysClient.getValueByOrgCodeAndConfigCode(orderMasterDTO.getFusercode(), New.list(ConfigCodeEnum.PURCHASE_STATEMENT_GRANULARITY.name()));
            isUnifiedSettlement = CollectionUtils.isNotEmpty(baseConfigDTOList) && this.isUnifiedSettlement(baseConfigDTOList.get(0).getConfigValue());
        }
        extraDTOList.add(new ExtraDTO("unifiedSettlement", isUnifiedSettlement.toString()));
        return extraDTOList;
    }

    /**
     * 从OMS配置获取是否统一结算
     * @param statementGranularity oms配置【结算粒度】
     * @return 是否统一结算
     */
    private boolean isUnifiedSettlement(String statementGranularity){
        if(StringUtils.isNotEmpty(statementGranularity)){
            String[] granularityArr = statementGranularity.split(",");
            for(String g : granularityArr){
                if ("4".equals(g) || "5".equals(g)) {
                    return true;
                }
            }
        }
        return false;
    }
}
